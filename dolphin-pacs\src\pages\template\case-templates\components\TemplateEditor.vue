<template>
  <div class="template-editor-page">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button
          link
          @click="goBack"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回模板列表
        </el-button>
        <div class="template-title">
          {{ templateName || '自定义报告编辑器' }}
        </div>
      </div>

      <!-- 编辑工具栏 -->
      <div class="toolbar-center">
        <el-button-group>
          <el-button @click="insertText" size="small">
            <el-icon><EditPen /></el-icon>
            文本
          </el-button>
          <el-button @click="insertTable" size="small">
            <el-icon><Grid /></el-icon>
            表格
          </el-button>
          <el-button @click="insertImage" size="small">
            <el-icon><Picture /></el-icon>
            图片
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button @click="setBold" size="small" :type="formatState.bold ? 'primary' : 'default'">
            <strong>B</strong>
          </el-button>
          <el-button @click="setItalic" size="small" :type="formatState.italic ? 'primary' : 'default'">
            <em>I</em>
          </el-button>
          <el-button @click="setUnderline" size="small" :type="formatState.underline ? 'primary' : 'default'">
            <u>U</u>
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-select v-model="currentFontSize" @change="setFontSize" size="small" style="width: 80px">
          <el-option v-for="size in fontSizes" :key="size" :label="size" :value="size" />
        </el-select>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button @click="undo" size="small" title="撤销">
            <el-icon><RefreshLeft /></el-icon>
          </el-button>
          <el-button @click="redo" size="small" title="重做">
            <el-icon><RefreshRight /></el-icon>
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button @click="alignLeft" size="small" title="左对齐">
            左对齐
          </el-button>
          <el-button @click="alignCenter" size="small" title="居中对齐">
            居中
          </el-button>
          <el-button @click="alignRight" size="small" title="右对齐">
            右对齐
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <el-button @click="handleSave" type="primary" size="small">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
        <el-button @click="handlePrint" size="small">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="handleExport" size="small">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 编辑器容器 -->
    <div class="editor-container">
      <div id="canvas-editor" class="canvas-editor-wrapper"></div>
    </div>

    <!-- 离开确认弹窗 -->
    <el-dialog
      v-model="leaveDialogVisible"
      title="离开确认"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="leave-content">
        <el-icon class="leave-icon" color="#f56c6c" :size="24">
          <Warning />
        </el-icon>
        <div class="leave-text">
          <p>确定要离开 <strong>"{{ templateName || '自定义报告编辑器' }}"</strong> 吗？</p>
          <p class="warning-text">未保存的更改将会丢失！</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="leaveDialogVisible = false">取消</el-button>
        <el-button
          type="danger"
          :loading="leaveLoading"
          @click="confirmLeave"
        >
          {{ leaveLoading ? '离开中...' : '确定离开' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 保存确认弹窗 -->
    <el-dialog
      v-model="saveDialogVisible"
      title="保存确认"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="save-content">
        <el-icon class="save-icon" color="#67c23a" :size="24">
          <Document />
        </el-icon>
        <div class="save-text">
          <p>确定要保存 <strong>"{{ templateName || '自定义报告编辑器' }}"</strong> 吗？</p>
          <p class="info-text">保存后可以随时继续编辑。</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="saveDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          @click="confirmSave"
        >
          {{ saveLoading ? '保存中...' : '确定保存' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 导出确认弹窗 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出文档"
      width="450px"
      :close-on-click-modal="false"
    >
      <div class="export-content">
        <el-icon class="export-icon" color="#409eff" :size="24">
          <Download />
        </el-icon>
        <div class="export-text">
          <p>选择导出格式：</p>
          <el-radio-group v-model="exportFormat" class="format-options">
            <el-radio value="pdf">
              <strong>PDF 格式</strong>
              <span class="format-desc">适合打印和分享</span>
            </el-radio>
            <el-radio value="word">
              <strong>Word 格式</strong>
              <span class="format-desc">可继续编辑</span>
            </el-radio>
          </el-radio-group>
          <p class="info-text">文件将下载到您的默认下载文件夹。</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="exportLoading"
          @click="confirmExport"
        >
          {{ exportLoading ? '导出中...' : `导出为 ${exportFormat.toUpperCase()}` }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Document, Printer, Download, EditPen, Grid, Picture, RefreshLeft, RefreshRight, Warning } from '@element-plus/icons-vue'
import CanvasEditor, { EditorMode } from '@hufe921/canvas-editor'

defineOptions({
  name: "TemplateEditor"
})

const route = useRoute()
const router = useRouter()

// 模板信息
const templateId = ref<string>(route.params.id as string)
const templateName = ref<string>(route.query.name as string)

// 编辑器实例
let editorInstance: any = null

// 编辑器状态
const formatState = ref({
  bold: false,
  italic: false,
  underline: false
})

const currentFontSize = ref(14)
const fontSizes = [10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48]

// 离开确认对话框状态
const leaveDialogVisible = ref(false)
const leaveLoading = ref(false)

// 保存确认对话框状态
const saveDialogVisible = ref(false)
const saveLoading = ref(false)

// 导出确认对话框状态
const exportDialogVisible = ref(false)
const exportLoading = ref(false)
const exportFormat = ref<'pdf' | 'word'>('pdf')

// 获取空白文档内容
const getBlankDocument = () => {
  return {
    main: [
      {
        value: '🎯 自定义报告编辑器使用指南\n\n',
        size: 18,
        bold: true,
        color: '#2c5aa0'
      },
      {
        value: '✨ 功能特点：\n',
        size: 16,
        bold: true,
        color: '#333333'
      },
      {
        value: '• 📝 点击"文本"按钮插入新的文本块\n',
        size: 14
      },
      {
        value: '• 📊 点击"表格"按钮插入3x3表格，可以调整大小\n',
        size: 14
      },
      {
        value: '• 🖼️ 点击"图片"按钮上传并插入图片\n',
        size: 14
      },
      {
        value: '• 🎨 使用粗体、斜体、下划线格式化文本\n',
        size: 14
      },
      {
        value: '• 📏 调整字体大小\n',
        size: 14
      },
      {
        value: '• ↩️ 撤销/重做操作\n',
        size: 14
      },
      {
        value: '• 📐 左对齐、居中、右对齐\n\n',
        size: 14
      },
      {
        value: '🚀 开始编辑：\n',
        size: 16,
        bold: true,
        color: '#333333'
      },
      {
        value: '选中这段文字，然后使用上方工具栏进行编辑。您可以拖拽移动文本块和表格！\n\n',
        size: 14,
        color: '#666666'
      },
      {
        value: '点击此处开始您的创作...',
        size: 14,
        color: '#999999'
      }
    ]
  }
}

// 初始化编辑器
const initEditor = () => {
  try {
    const container = document.getElementById('canvas-editor') as HTMLDivElement
    if (!container) return

    // 获取空白文档内容
    const documentContent = getBlankDocument()

    // 初始化编辑器
    editorInstance = new CanvasEditor(container, documentContent, {
      height: 800,
      mode: EditorMode.EDIT,
      header: {
        disabled: false,
        top: 50
      },
      footer: {
        disabled: false,
        bottom: 50
      },
      margins: [100, 120, 100, 120],
      watermark: {
        data: '医院超声科',
        opacity: 0.1,
        size: 100,
        color: '#cccccc'
      }
    })

    // 监听编辑器事件
    setupEditorEvents()

    ElMessage.success('编辑器加载成功')
  } catch (error) {
    console.error('编辑器初始化失败:', error)
    ElMessage.error('编辑器初始化失败')
  }
}

// 设置编辑器事件监听
const setupEditorEvents = () => {
  if (!editorInstance) return

  // 监听选择变化，更新格式状态
  editorInstance.listener.rangeStyleChange = (payload: any) => {
    if (payload) {
      formatState.value.bold = payload.bold || false
      formatState.value.italic = payload.italic || false
      formatState.value.underline = payload.underline || false
      currentFontSize.value = payload.size || 14
    }
  }

  // 监听内容变化
  editorInstance.listener.contentChange = () => {
    // 可以在这里添加自动保存逻辑
    console.log('内容已变化')
  }
}

// 编辑器功能方法
const insertText = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  // 插入文本块
  editorInstance.command.executeInsertElementList([
    {
      value: '新文本块 ',
      size: 14
    }
  ])
  ElMessage.success('文本插入成功')
}

const insertTable = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  // 插入3x3表格
  editorInstance.command.executeInsertTable(3, 3)
  ElMessage.success('表格插入成功')
}

const insertImage = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  // 创建文件输入
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (event: any) => {
        // 使用正确的 API 插入图片
        editorInstance.command.executeImage({
          width: 200,
          height: 150,
          value: event.target.result
        })
        ElMessage.success('图片插入成功')
      }
      reader.readAsDataURL(file)
    }
  }
  input.click()
}

const setBold = () => {
  if (!editorInstance) return
  editorInstance.command.executeBold()
}

const setItalic = () => {
  if (!editorInstance) return
  editorInstance.command.executeItalic()
}

const setUnderline = () => {
  if (!editorInstance) return
  editorInstance.command.executeUnderline()
}

const setFontSize = (size: number) => {
  if (!editorInstance) return
  editorInstance.command.executeSize(size)
}

// 撤销和重做
const undo = () => {
  if (!editorInstance) return
  editorInstance.command.executeUndo()
}

const redo = () => {
  if (!editorInstance) return
  editorInstance.command.executeRedo()
}

// 对齐功能
const alignLeft = () => {
  if (!editorInstance) return
  editorInstance.command.executeRowFlex('left')
}

const alignCenter = () => {
  if (!editorInstance) return
  editorInstance.command.executeRowFlex('center')
}

const alignRight = () => {
  if (!editorInstance) return
  editorInstance.command.executeRowFlex('right')
}

// 返回模板列表
const goBack = () => {
  leaveDialogVisible.value = true
}

// 确认离开
const confirmLeave = async () => {
  try {
    leaveLoading.value = true
    // 这里可以添加保存检查逻辑
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟异步操作
    leaveDialogVisible.value = false
    router.go(-1)
  } catch (error) {
    console.error('离开失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    leaveLoading.value = false
  }
}

// 保存文档
const handleSave = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }
  saveDialogVisible.value = true
}

// 确认保存
const confirmSave = async () => {
  try {
    saveLoading.value = true
    const content = editorInstance.getValue()
    // 这里可以发送请求保存到服务器
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟保存过程
    console.log('保存内容:', content)
    ElMessage.success('保存成功')
    saveDialogVisible.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 打印文档
const handlePrint = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }

  try {
    editorInstance.print()
    ElMessage.success('准备打印')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印功能暂不可用')
  }
}

// 导出文档
const handleExport = () => {
  if (!editorInstance) {
    ElMessage.error('编辑器未初始化')
    return
  }
  exportDialogVisible.value = true
}

// 确认导出
const confirmExport = async () => {
  try {
    exportLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟导出过程

    if (exportFormat.value === 'pdf') {
      // 导出 PDF
      try {
        editorInstance.exportPDF(`${templateName.value || '报告'}.pdf`)
        ElMessage.success('PDF 导出成功')
      } catch (error) {
        ElMessage.error('PDF 导出失败')
      }
    } else {
      // 导出 Word
      try {
        editorInstance.exportWord(`${templateName.value || '报告'}.docx`)
        ElMessage.success('Word 导出成功')
      } catch (error) {
        ElMessage.error('Word 导出失败')
      }
    }

    exportDialogVisible.value = false
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 延迟初始化编辑器，确保DOM已渲染
  setTimeout(initEditor, 100)
})



onUnmounted(() => {
  // 清理编辑器实例
  if (editorInstance && typeof editorInstance.destroy === 'function') {
    editorInstance.destroy()
  }
})
</script>

<style lang="scss" scoped>
.template-editor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  gap: 16px;
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;

  .back-btn {
    color: #409eff;
    font-size: 14px;
    padding: 8px 0;

    &:hover {
      color: #66b1ff;
    }
  }

  .template-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.toolbar-right {
  display: flex;
  gap: 8px;

  .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.editor-container {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.canvas-editor-wrapper {
  width: 100%;
  max-width: 1000px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// 响应式布局
@media screen and (max-width: 768px) {
  .editor-toolbar {
    padding: 8px 16px;
    flex-direction: column;
    gap: 12px;
  }

  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .toolbar-center {
    flex-wrap: wrap;
    gap: 8px;
  }

  .editor-container {
    padding: 10px;
  }

  .canvas-editor-wrapper {
    max-width: 100%;
  }
}

// 对话框样式
.leave-content,
.save-content,
.export-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .leave-icon,
  .save-icon,
  .export-icon {
    flex-shrink: 0;
    margin-top: 2px;
  }

  .leave-text,
  .save-text,
  .export-text {
    flex: 1;

    p {
      margin: 0 0 8px 0;
      line-height: 1.5;
    }

    .warning-text {
      color: #f56c6c;
      font-weight: bold;
      margin-top: 12px !important;
    }

    .info-text {
      color: #909399;
      font-size: 13px;
      margin-top: 8px !important;
    }
  }
}

// 导出格式选择样式
.format-options {
  margin: 12px 0;

  .el-radio {
    display: block;
    margin: 8px 0;

    .el-radio__label {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .format-desc {
        font-size: 12px;
        color: #909399;
        font-weight: normal;
        margin-top: 2px;
      }
    }
  }
}
</style>